"use client";

import React from "react";
import { PlayCircleIcon, SpeechIcon } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

function SideMenu() {
  const pathname = usePathname();
  const router = useRouter();

  return (
    <div className="z-[10] bg-black/20 backdrop-blur-lg border-r border-white/10 p-6 w-[200px] fixed top-[64px] left-0 h-full animate-slide-in">
      <div className="flex flex-col gap-1">
        <div className="flex flex-col justify-between gap-2">
          <div
            className={`flex flex-row p-3 rounded-lg transition-all duration-300 cursor-pointer group hover:scale-105 ${
              pathname.endsWith("/dashboard") ||
              pathname.includes("/interviews")
                ? "bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-400/30 shadow-lg"
                : "bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20"
            }`}
            onClick={() => router.push("/dashboard")}
          >
            <PlayCircleIcon className="font-thin mr-2 text-purple-400 group-hover:text-purple-300 transition-colors duration-300" />
            <p className="font-medium text-white group-hover:text-purple-200 transition-colors duration-300">Interviews</p>
          </div>
          <div
            className={`flex flex-row p-3 rounded-lg transition-all duration-300 cursor-pointer group hover:scale-105 ${
              pathname.endsWith("/interviewers")
                ? "bg-gradient-to-r from-purple-500/20 to-blue-500/20 border border-purple-400/30 shadow-lg"
                : "bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20"
            }`}
            onClick={() => router.push("/dashboard/interviewers")}
          >
            <SpeechIcon className="font-thin mr-2 text-blue-400 group-hover:text-blue-300 transition-colors duration-300" />
            <p className="font-medium text-white group-hover:text-blue-200 transition-colors duration-300">Interviewers</p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SideMenu;

import Link from "next/link";
import React from "react";
import { OrganizationSwitcher, User<PERSON><PERSON>on } from "@clerk/nextjs";

function Navbar() {
  return (
    <div className="fixed inset-x-0 top-0 bg-black/20 backdrop-blur-lg border-b border-white/10 z-[10] h-fit py-4 animate-fade-in">
      <div className="flex items-center justify-between h-full gap-2 px-8 mx-auto">
        <div className="flex flex-row gap-3 justify-center">
          <Link href={"/dashboard"} className="flex items-center gap-2 group">
            <p className="px-3 py-2 text-2xl font-bold bg-gradient-to-r from-purple-400 via-blue-400 to-purple-400 bg-clip-text text-transparent hover:from-purple-300 hover:via-blue-300 hover:to-purple-300 transition-all duration-300 group-hover:scale-105">
              Folo<span className="bg-gradient-to-r from-indigo-400 to-purple-400 bg-clip-text text-transparent">Up</span>{" "}
              <span className="text-[8px] bg-gradient-to-r from-pink-400 to-red-400 bg-clip-text text-transparent">Beta</span>
            </p>
          </Link>
          <p className="my-auto text-xl text-white/60">/</p>
          <div className="my-auto">
            <OrganizationSwitcher
              afterCreateOrganizationUrl="/dashboard"
              hidePersonal={true}
              afterSelectOrganizationUrl="/dashboard"
              afterLeaveOrganizationUrl="/dashboard"
              appearance={{
                variables: {
                  fontSize: "0.9rem",
                },
                elements: {
                  organizationSwitcherTrigger: "bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300",
                  organizationSwitcherPopoverCard: "bg-black/80 backdrop-blur-lg border border-white/20",
                }
              }}
            />
          </div>
        </div>
        <div className="flex items-center">
          <div className="p-1 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20 transition-all duration-300">
            <UserButton afterSignOutUrl="/sign-in" signInUrl="/sign-in" />
          </div>
        </div>
      </div>
    </div>
  );
}

export default Navbar;
